// 免费模板页面
const freeTemplateApi = require('../../utils/api/freeTemplateApi');
const app = getApp();

Page({
  data: {
    templates: [],           // 模板列表
    isLoading: false,        // 是否正在加载
    isLoadingMore: false,    // 是否正在加载更多
    hasMore: true,           // 是否还有更多数据
    skip: 0,                 // 跳过的记录数
    limit: 20,               // 每次加载数量

    // 下载浮窗相关
    showDownloadModal: false,    // 是否显示下载浮窗
    selectedTemplate: null,      // 选中的模板
    downloadData: null,          // 下载数据
    isLoadingDownload: false,    // 是否正在加载下载链接
  },

  onLoad() {
    this.loadMoreTemplates();
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.templates.length === 0) {
      this.loadMoreTemplates();
    }
  },



  /**
   * 加载更多模板（统一的模板加载方法）
   */
  async loadMoreTemplates() {
    // 如果是初次加载（templates为空），使用isLoading状态
    const isFirstLoad = this.data.templates.length === 0;
    const loadingStateKey = isFirstLoad ? 'isLoading' : 'isLoadingMore';

    // 防止重复加载
    if (this.data[loadingStateKey] || (!isFirstLoad && !this.data.hasMore)) return;

    try {
      this.setData({
        [loadingStateKey]: true
      });

      const response = await freeTemplateApi.getFreeTemplateList({
        skip: this.data.skip,
        limit: this.data.limit,
        type: 'word'
      });

      if (response && response.templates) {
        console.log(`服务器返回的templates数量: ${response.templates.length}`);
        const newTemplates = (response.templates || []).map((template, index) => {
          // 兼容处理：确保必要字段存在
          const processedTemplate = {
            id: template.id || '',
            thumb_url: template.thumb_url || '',
            baidu_url: template.baidu_url || '',
            baidu_pass: template.baidu_pass || '',
            quark_url: template.quark_url || '',
            quark_pass: template.quark_pass || '',
            type: template.type || 'word',
            // 添加UI状态字段
            isLoading: true,  // 初始化时设置为加载状态
            imageError: false, // 初始化错误状态
            // 添加唯一标识符，确保wx:key不重复
            uniqueKey: `${template.id || 'template'}_${Date.now()}_${index}_${Math.random().toString(36).substring(2, 11)}`
          };



          return processedTemplate;
        });

        // 去重处理：基于id字段去重，避免重复数据
        const existingIds = new Set(this.data.templates.map(t => t.id));
        const uniqueNewTemplates = newTemplates.filter(template => !existingIds.has(template.id));

        // 显示newTemplates的id
        // console.log('newTemplates 的id:', newTemplates.map(t => t.id));

        // 显示重复的id
        // if (newTemplates.length !== uniqueNewTemplates.length) {
        //   console.log('存在重复的模板数据');
        //   console.log('服务器返回的重复数据:', newTemplates.filter(template => existingIds.has(template.id)));
        // }

        const finalTemplates = [...this.data.templates, ...uniqueNewTemplates];
        // console.log(`服务器的total: ${response.total}`);
        console.log(`当前finalTemplates数量: ${finalTemplates.length}`);
        // console.log(`finalTemplates 的id:`, finalTemplates.map(t => t.id));
        const total = response.total || 0;
        const hasMore = finalTemplates.length < total;

        this.setData({
          templates: finalTemplates,
          hasMore: hasMore,
          // 修复skip计算：应该基于服务器实际返回的数量，而不是去重后的数量
          skip: this.data.skip + newTemplates.length,
          [loadingStateKey]: false
        });
      }
    } catch (error) {
      console.error('加载模板失败:', error);
      this.setData({
        [loadingStateKey]: false
      });

      // 简化错误处理，只使用微信官方Toast提示
      wx.showToast({
        title: '加载更多失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 上拉加载更多
   */
  onLoadMore() {
    this.loadMoreTemplates();
  },

  /**
   * 重试加载
   */
  onRetry() {
    this.setData({
      templates: [],
      skip: 0,
      hasMore: true
    });
    this.loadMoreTemplates();
  },

  /**
   * 图片加载成功
   */
  onImageLoad(e) {
    const index = e.currentTarget.dataset.index;
    const templates = this.data.templates;
    if (templates[index]) {
      templates[index].isLoading = false;
      this.setData({
        templates: templates
      });
    }
  },

  /**
   * 图片加载失败
   */
  onImageError(e) {
    const index = e.currentTarget.dataset.index;
    const templates = this.data.templates;
    if (templates[index]) {
      const template = templates[index];
      template.isLoading = false;
      template.imageError = true; // 标记图片加载失败

      // 使用base64编码的占位符图片，避免路径问题
      template.thumb_url = 'data:image/svg+xml;base64,' + btoa(`
        <svg width="280" height="400" xmlns="http://www.w3.org/2000/svg">
          <rect width="280" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
          <g transform="translate(140, 150)">
            <rect x="-30" y="-40" width="50" height="70" fill="#6c757d" rx="3"/>
            <polygon points="20,-40 20,-25 35,-25" fill="#495057"/>
            <line x1="-20" y1="-25" x2="10" y2="-25" stroke="white" stroke-width="2"/>
            <line x1="-20" y1="-15" x2="15" y2="-15" stroke="white" stroke-width="2"/>
            <line x1="-20" y1="-5" x2="15" y2="-5" stroke="white" stroke-width="2"/>
            <line x1="-20" y1="5" x2="10" y2="5" stroke="white" stroke-width="2"/>
          </g>
          <text x="140" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#6c757d">暂无图片</text>
        </svg>
      `);

      this.setData({
        templates: templates
      });
    }
  },

  /**
   * 点击模板
   */
  onTemplateClick(e) {
    const template = e.currentTarget.dataset.template;

    if (!template || !template.id) {
      wx.showToast({
        title: '模板信息无效',
        icon: 'none'
      });
      return;
    }

    // 检查模板是否包含下载链接
    if (!template.baidu_url && !template.quark_url) {
      // 上报模板下载链接缺失错误
      app.reportError('free_template_no_download_link', '模板缺少下载链接', {
        page: 'freeResume/index',
        action: 'onTemplateClick',
        template_id: template.id,
        template_name: template.name
      });

      wx.showToast({
        title: '暂无下载链接',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 直接使用模板数据中的下载链接信息
    const downloadData = {
      baidu_url: template.baidu_url,
      baidu_pass: template.baidu_pass,
      quark_url: template.quark_url,
      quark_pass: template.quark_pass
    };

    // 设置选中的模板并显示浮窗
    this.setData({
      selectedTemplate: template,
      showDownloadModal: true,
      downloadData: downloadData,
      isLoadingDownload: false
    });
  },

  /**
   * 关闭下载浮窗
   */
  onCloseDownloadModal() {
    this.setData({
      showDownloadModal: false,
      selectedTemplate: null,
      downloadData: null,
      isLoadingDownload: false
    });
  },

  /**
   * 复制成功回调
   */
  onCopySuccess() {
    // 可以在这里添加统计逻辑
  },

  /**
   * 复制失败回调
   */
  onCopyFail(e) {
    const { type, error } = e.detail;
    console.error('复制失败:', type, error);
  },





  /**
   * 页面分享
   */
  onShareAppMessage() {
    // 如果有动态设置的分享数据，使用它
    if (this.shareData) {
      const data = this.shareData;
      this.shareData = null; // 清除临时数据
      return data;
    }

    // 如果当前有选中的模板，使用模板信息进行分享
    if (this.data.selectedTemplate) {
      return {
        title: '个人简历模板 - 免费模板下载',
        path: '/pages/freeResume/index',
        imageUrl: this.data.selectedTemplate.thumb_url || './images/share-cover.png'
      };
    }

    // 默认分享数据
    return {
      title: '免费简历模板下载',
      path: '/pages/freeResume/index',
      imageUrl: './images/share-cover.png'
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '免费简历模板下载 - 精美Word模板',
      imageUrl: './images/share-cover.png'
    };
  }
});
